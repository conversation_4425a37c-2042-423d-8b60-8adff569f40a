<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>2D to 3D Environment Generator</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://threejs.org/examples/js/controls/OrbitControls.js"></script>
    <script src="https://threejs.org/examples/js/exporters/GLTFExporter.js"></script>
    <script src="https://threejs.org/examples/js/utils/BufferGeometryUtils.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tensorflow/3.18.0/tf.min.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>2D Image to 3D Environment Generator</h1>
            <p>Upload a 2D image and convert it to a 3D environment exportable to Blender</p>
        </header>

        <main>
            <div class="upload-section">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-content">
                        <svg class="upload-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                            <polyline points="7,10 12,15 17,10"></polyline>
                            <line x1="12" y1="15" x2="12" y2="3"></line>
                        </svg>
                        <h3>Drop your image here or click to browse</h3>
                        <p>Supports JPG, PNG, WebP formats</p>
                    </div>
                    <input type="file" id="imageInput" accept="image/*" hidden>
                </div>
                
                <div class="image-preview" id="imagePreview" style="display: none;">
                    <img id="previewImg" alt="Preview">
                    <button id="removeImage" class="remove-btn">×</button>
                </div>
            </div>

            <div class="controls-section">
                <div class="control-group">
                    <label for="depthStrength">Depth Strength:</label>
                    <input type="range" id="depthStrength" min="0.1" max="2.0" step="0.1" value="1.0">
                    <span id="depthValue">1.0</span>
                </div>
                
                <div class="control-group">
                    <label for="meshResolution">Mesh Resolution:</label>
                    <select id="meshResolution">
                        <option value="64">Low (64x64)</option>
                        <option value="128" selected>Medium (128x128)</option>
                        <option value="256">High (256x256)</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label for="environmentType">Environment Type:</label>
                    <select id="environmentType">
                        <option value="landscape">Landscape</option>
                        <option value="room">Indoor Room</option>
                        <option value="terrain">Terrain</option>
                    </select>
                </div>
                
                <button id="generateBtn" class="generate-btn" disabled>Generate 3D Environment</button>
            </div>

            <div class="viewer-section">
                <div class="viewer-container">
                    <canvas id="threejsCanvas"></canvas>
                    <div class="viewer-controls">
                        <button id="resetView">Reset View</button>
                        <button id="exportGLB" disabled>Export GLB</button>
                    </div>
                </div>
                
                <div class="progress-section" id="progressSection" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <p id="progressText">Processing...</p>
                </div>
            </div>
        </main>

        <footer>
            <p>Built with Three.js and TensorFlow.js</p>
        </footer>
    </div>

    <script src="depthEstimation.js"></script>
    <script src="meshGenerator.js"></script>
    <script src="glbExporter.js"></script>
    <script src="app.js"></script>
</body>
</html>

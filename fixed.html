<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fixed 2D to 3D Converter</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .upload-section {
            border: 3px dashed #ddd;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            background: #fafafa;
            margin: 20px 0;
        }
        .file-input {
            font-size: 16px;
            padding: 10px;
            margin: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            background: white;
        }
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #45a049;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .btn.secondary {
            background: #2196F3;
        }
        .btn.danger {
            background: #f44336;
        }
        .preview {
            text-align: center;
            margin: 20px 0;
        }
        .preview img {
            max-width: 300px;
            max-height: 200px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .controls {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .control-group {
            margin: 15px 0;
        }
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .control-group input, .control-group select {
            width: 100%;
            padding: 8px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .viewer-section {
            margin: 20px 0;
            text-align: center;
        }
        #canvas3d {
            border: 2px solid #333;
            border-radius: 8px;
            background: #000;
        }
        .viewer-controls {
            margin: 10px 0;
        }
        .log {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 11px;
            max-height: 150px;
            overflow-y: auto;
            margin: 20px 0;
            border: 1px solid #ddd;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 2D to 3D Environment Generator (Fixed)</h1>
        
        <div class="upload-section">
            <h3>📁 Upload Your Image</h3>
            <input type="file" id="imageFile" class="file-input" accept="image/*">
            <br>
            <button onclick="loadSelectedImage()" class="btn">Load Image</button>
        </div>
        
        <div id="imagePreview" class="preview" style="display: none;">
            <h3>📷 Image Preview</h3>
            <img id="previewImg" alt="Preview">
            <br>
            <button onclick="clearImage()" class="btn danger">Remove Image</button>
        </div>
        
        <div id="controls" class="controls" style="display: none;">
            <h3>⚙️ 3D Generation Settings</h3>
            
            <div class="control-group">
                <label for="depthStrength">Depth Strength:</label>
                <input type="range" id="depthStrength" min="0.5" max="5.0" step="0.1" value="2.0">
                <span id="depthValue">2.0</span>
            </div>
            
            <div class="control-group">
                <label for="meshResolution">Mesh Quality:</label>
                <select id="meshResolution">
                    <option value="32">Low (32x32) - Fast</option>
                    <option value="64" selected>Medium (64x64) - Balanced</option>
                    <option value="96">High (96x96) - Detailed</option>
                </select>
            </div>
            
            <button onclick="generate3DEnvironment()" class="btn" id="generateBtn">🚀 Generate 3D Environment</button>
        </div>
        
        <div id="status"></div>
        
        <div class="viewer-section" id="viewerSection" style="display: none;">
            <h3>🎮 3D Environment Viewer</h3>
            <canvas id="canvas3d" width="700" height="500"></canvas>
            <div class="viewer-controls">
                <button onclick="resetCamera()" class="btn secondary">🔄 Reset View</button>
                <button onclick="toggleWireframe()" class="btn secondary">📐 Toggle Wireframe</button>
                <button onclick="exportToGLB()" class="btn" id="exportBtn">📦 Export GLB</button>
            </div>
            <p><strong>Controls:</strong> Left mouse = Orbit | Right mouse = Pan | Scroll = Zoom</p>
        </div>
        
        <div class="log" id="log">
            <strong>📋 Activity Log:</strong><br>
            [Ready] Application loaded successfully<br>
        </div>
    </div>

    <!-- Three.js Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://threejs.org/examples/js/controls/OrbitControls.js"></script>
    <script src="https://threejs.org/examples/js/exporters/GLTFExporter.js"></script>

    <script>
        // Global variables
        let currentImage = null;
        let scene, camera, renderer, controls, generatedMesh;
        let wireframeMode = false;
        
        // Logging function
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        // Status display function
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
            if (type !== 'error') {
                setTimeout(() => statusDiv.innerHTML = '', 4000);
            }
        }
        
        // Load selected image
        function loadSelectedImage() {
            const fileInput = document.getElementById('imageFile');
            const file = fileInput.files[0];
            
            if (!file) {
                showStatus('❌ Please select an image file first', 'error');
                return;
            }
            
            if (!file.type.startsWith('image/')) {
                showStatus('❌ Please select a valid image file', 'error');
                return;
            }
            
            log(`Loading: ${file.name} (${(file.size/1024).toFixed(1)}KB)`);
            showStatus('📖 Loading image...', 'info');
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    currentImage = img;
                    document.getElementById('previewImg').src = e.target.result;
                    document.getElementById('imagePreview').style.display = 'block';
                    document.getElementById('controls').style.display = 'block';
                    
                    log(`✅ Loaded: ${img.width}x${img.height}`);
                    showStatus('✅ Image ready for 3D conversion!', 'success');
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
        
        // Clear image
        function clearImage() {
            currentImage = null;
            document.getElementById('imageFile').value = '';
            document.getElementById('imagePreview').style.display = 'none';
            document.getElementById('controls').style.display = 'none';
            document.getElementById('viewerSection').style.display = 'none';
            log('Image cleared');
        }
        
        // Update depth value display
        document.getElementById('depthStrength').addEventListener('input', function(e) {
            document.getElementById('depthValue').textContent = e.target.value;
        });
        
        // Generate 3D Environment
        function generate3DEnvironment() {
            if (!currentImage) {
                showStatus('❌ Please load an image first', 'error');
                return;
            }
            
            log('🚀 Starting 3D generation...');
            showStatus('🔄 Generating 3D environment...', 'info');
            
            try {
                // Initialize Three.js
                initializeThreeJS();
                
                // Get settings
                const depthStrength = parseFloat(document.getElementById('depthStrength').value);
                const resolution = parseInt(document.getElementById('meshResolution').value);
                
                log(`Settings: Depth=${depthStrength}, Resolution=${resolution}x${resolution}`);
                
                // Create depth map and mesh
                const depthData = createDepthMap(currentImage);
                create3DMesh(depthData, resolution, depthStrength);
                
                // Show viewer
                document.getElementById('viewerSection').style.display = 'block';
                
                // Position camera to see the mesh
                camera.position.set(0, 15, 25);
                camera.lookAt(0, 0, 0);
                controls.update();
                
                log('✅ 3D environment generated!');
                showStatus('🎉 3D environment ready! Use mouse to explore.', 'success');
                
            } catch (error) {
                log('ERROR: ' + error.message);
                console.error(error);
                showStatus('❌ Generation failed: ' + error.message, 'error');
            }
        }
        
        // Initialize Three.js
        function initializeThreeJS() {
            const canvas = document.getElementById('canvas3d');
            
            // Clear previous scene
            if (scene) {
                while(scene.children.length > 0) {
                    scene.remove(scene.children[0]);
                }
            }
            
            // Scene
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x87CEEB); // Sky blue
            
            // Camera
            camera = new THREE.PerspectiveCamera(60, canvas.width / canvas.height, 0.1, 1000);
            camera.position.set(0, 15, 25);
            
            // Renderer
            renderer = new THREE.WebGLRenderer({ canvas: canvas, antialias: true });
            renderer.setSize(canvas.width, canvas.height);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            
            // Controls
            controls = new THREE.OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;
            controls.target.set(0, 0, 0);
            
            // Enhanced lighting
            const ambientLight = new THREE.AmbientLight(0x404040, 0.8);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
            directionalLight.position.set(20, 20, 10);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);
            
            // Additional point light
            const pointLight = new THREE.PointLight(0xffffff, 0.5, 100);
            pointLight.position.set(0, 20, 0);
            scene.add(pointLight);
            
            // Add reference objects to help with scale
            addReferenceObjects();
            
            // Start animation
            animate();
            
            log('✅ 3D engine initialized');
        }
        
        // Add reference objects for scale
        function addReferenceObjects() {
            // Ground plane for reference
            const groundGeometry = new THREE.PlaneGeometry(50, 50);
            const groundMaterial = new THREE.MeshLambertMaterial({ 
                color: 0x90EE90, 
                transparent: true, 
                opacity: 0.3 
            });
            const ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.position.y = -2;
            scene.add(ground);
            
            // Coordinate axes helper
            const axesHelper = new THREE.AxesHelper(10);
            scene.add(axesHelper);
        }
        
        // Animation loop
        function animate() {
            requestAnimationFrame(animate);
            if (controls) controls.update();
            if (renderer && scene && camera) renderer.render(scene, camera);
        }
        
        // Create depth map
        function createDepthMap(img) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            // Use smaller canvas for processing
            const maxSize = 512;
            const scale = Math.min(maxSize / img.naturalWidth, maxSize / img.naturalHeight);
            canvas.width = Math.floor(img.naturalWidth * scale);
            canvas.height = Math.floor(img.naturalHeight * scale);
            
            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
            
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;
            const depthMap = new Float32Array(canvas.width * canvas.height);
            
            // Enhanced depth calculation
            for (let i = 0; i < data.length; i += 4) {
                const r = data[i];
                const g = data[i + 1];
                const b = data[i + 2];
                
                // Weighted luminance for better depth perception
                const luminance = 0.299 * r + 0.587 * g + 0.114 * b;
                
                // Normalize and enhance contrast
                let depth = luminance / 255;
                depth = Math.pow(depth, 0.8); // Gamma correction for better contrast
                
                depthMap[i / 4] = depth;
            }
            
            log(`Depth map created: ${canvas.width}x${canvas.height}`);
            
            return {
                depthMap: depthMap,
                width: canvas.width,
                height: canvas.height,
                texture: new THREE.CanvasTexture(canvas)
            };
        }
        
        // Create 3D mesh
        function create3DMesh(depthData, resolution, depthStrength) {
            // Remove existing mesh
            if (generatedMesh) {
                scene.remove(generatedMesh);
            }
            
            // Create plane geometry
            const size = 20;
            const geometry = new THREE.PlaneGeometry(size, size, resolution - 1, resolution - 1);
            const vertices = geometry.attributes.position.array;
            
            log(`Creating mesh: ${resolution}x${resolution} vertices`);
            
            // Apply depth displacement
            let minHeight = Infinity, maxHeight = -Infinity;
            
            for (let i = 0; i < vertices.length; i += 3) {
                const x = vertices[i];
                const z = vertices[i + 2];
                
                // Map to image coordinates
                const imgX = Math.floor(((x + size/2) / size) * depthData.width);
                const imgY = Math.floor(((z + size/2) / size) * depthData.height);
                const index = Math.min(imgY * depthData.width + imgX, depthData.depthMap.length - 1);
                
                // Apply depth with strength multiplier
                const height = depthData.depthMap[index] * depthStrength * 5;
                vertices[i + 1] = height;
                
                minHeight = Math.min(minHeight, height);
                maxHeight = Math.max(maxHeight, height);
            }
            
            geometry.attributes.position.needsUpdate = true;
            geometry.computeVertexNormals();
            
            log(`Height range: ${minHeight.toFixed(2)} to ${maxHeight.toFixed(2)}`);
            
            // Create material
            const material = new THREE.MeshLambertMaterial({
                map: depthData.texture,
                side: THREE.DoubleSide
            });
            
            // Create mesh
            generatedMesh = new THREE.Mesh(geometry, material);
            generatedMesh.receiveShadow = true;
            generatedMesh.castShadow = true;
            scene.add(generatedMesh);
            
            log(`✅ Mesh created with ${vertices.length/3} vertices`);
        }
        
        // Reset camera
        function resetCamera() {
            if (camera && controls) {
                camera.position.set(0, 15, 25);
                camera.lookAt(0, 0, 0);
                controls.target.set(0, 0, 0);
                controls.update();
                log('Camera reset');
            }
        }
        
        // Toggle wireframe
        function toggleWireframe() {
            if (generatedMesh) {
                wireframeMode = !wireframeMode;
                generatedMesh.material.wireframe = wireframeMode;
                log(`Wireframe: ${wireframeMode ? 'ON' : 'OFF'}`);
            }
        }
        
        // Export to GLB
        function exportToGLB() {
            if (!generatedMesh) {
                showStatus('❌ No 3D environment to export', 'error');
                return;
            }
            
            log('📦 Exporting GLB...');
            showStatus('📦 Preparing GLB export...', 'info');
            
            const exporter = new THREE.GLTFExporter();
            
            exporter.parse(scene, function(result) {
                const blob = new Blob([result], { type: 'application/octet-stream' });
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                
                const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
                const filename = `3d_environment_${timestamp}.glb`;
                
                link.href = url;
                link.download = filename;
                link.click();
                
                URL.revokeObjectURL(url);
                
                log(`✅ Exported: ${filename}`);
                showStatus(`🎉 Downloaded: ${filename}`, 'success');
                
            }, { binary: true, embedImages: true });
        }
        
        log('🎯 Application ready!');
    </script>
</body>
</html>

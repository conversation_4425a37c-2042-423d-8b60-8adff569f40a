<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Test - Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .upload-area {
            border: 3px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #fafafa;
            margin: 20px 0;
        }
        .upload-area:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }
        .upload-area.dragover {
            border-color: #667eea;
            background: #e8f0ff;
            transform: scale(1.02);
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .preview {
            margin: 20px 0;
            text-align: center;
        }
        .preview img {
            max-width: 300px;
            max-height: 200px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Upload Test - Debug Page</h1>
        <p>This page helps debug the image upload functionality.</p>
        
        <div class="upload-area" id="uploadArea">
            <h3>Drop your image here or click to browse</h3>
            <p>Supports JPG, PNG, WebP formats</p>
            <input type="file" id="imageInput" accept="image/*" hidden>
        </div>
        
        <div class="preview" id="preview" style="display: none;">
            <img id="previewImg" alt="Preview">
            <p id="imageInfo"></p>
        </div>
        
        <button onclick="clearLog()">Clear Log</button>
        <button onclick="testFileInput()">Test File Input</button>
        
        <div class="log" id="log"></div>
    </div>

    <script>
        let logElement = document.getElementById('log');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            logElement.textContent = '';
        }
        
        function testFileInput() {
            const input = document.getElementById('imageInput');
            log('Testing file input click...');
            input.click();
        }
        
        // Initialize upload functionality
        const uploadArea = document.getElementById('uploadArea');
        const imageInput = document.getElementById('imageInput');
        const preview = document.getElementById('preview');
        const previewImg = document.getElementById('previewImg');
        const imageInfo = document.getElementById('imageInfo');
        
        log('Initializing upload test page...');
        
        // Click handler
        uploadArea.addEventListener('click', () => {
            log('Upload area clicked');
            imageInput.click();
        });
        
        // Drag and drop handlers
        uploadArea.addEventListener('dragenter', (e) => {
            e.preventDefault();
            e.stopPropagation();
            uploadArea.classList.add('dragover');
            log('Drag enter');
        });
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.stopPropagation();
            log('Drag over');
        });
        
        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            e.stopPropagation();
            if (!uploadArea.contains(e.relatedTarget)) {
                uploadArea.classList.remove('dragover');
                log('Drag leave');
            }
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            e.stopPropagation();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            log(`Drop event - ${files.length} files`);
            
            if (files.length > 0) {
                const file = files[0];
                log(`File dropped: ${file.name} (${file.type}, ${file.size} bytes)`);
                loadImage(file);
            }
        });
        
        // File input change handler
        imageInput.addEventListener('change', (e) => {
            log('File input changed');
            const file = e.target.files[0];
            if (file) {
                log(`File selected: ${file.name} (${file.type}, ${file.size} bytes)`);
                loadImage(file);
            } else {
                log('No file selected');
            }
        });
        
        // Prevent default drag behaviors on document
        document.addEventListener('dragover', (e) => e.preventDefault());
        document.addEventListener('drop', (e) => e.preventDefault());
        
        function loadImage(file) {
            log(`Loading image: ${file.name}`);
            
            if (!file.type.startsWith('image/')) {
                log('ERROR: Not an image file');
                alert('Please select a valid image file.');
                return;
            }
            
            const reader = new FileReader();
            reader.onload = (e) => {
                log('File read successfully');
                const img = new Image();
                img.onload = () => {
                    log(`Image loaded: ${img.width}x${img.height}`);
                    previewImg.src = e.target.result;
                    imageInfo.textContent = `${file.name} - ${img.width}x${img.height} - ${(file.size/1024).toFixed(1)}KB`;
                    preview.style.display = 'block';
                };
                img.onerror = () => {
                    log('ERROR: Failed to load image');
                    alert('Failed to load the image.');
                };
                img.src = e.target.result;
            };
            reader.onerror = () => {
                log('ERROR: Failed to read file');
                alert('Failed to read the file.');
            };
            reader.readAsDataURL(file);
        }
        
        log('Upload test page ready!');
    </script>
</body>
</html>

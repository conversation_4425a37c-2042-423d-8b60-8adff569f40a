class MeshGenerator {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.generatedMesh = null;
    }

    initializeThreeJS(canvas) {
        // Scene setup
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x222222);

        // Camera setup
        this.camera = new THREE.PerspectiveCamera(
            75,
            canvas.clientWidth / canvas.clientHeight,
            0.1,
            1000
        );
        this.camera.position.set(0, 5, 10);

        // Renderer setup
        this.renderer = new THREE.WebGLRenderer({ 
            canvas: canvas,
            antialias: true 
        });
        this.renderer.setSize(canvas.clientWidth, canvas.clientHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

        // Controls
        this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;

        // Lighting
        this.setupLighting();

        // Start render loop
        this.animate();
    }

    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);

        // Directional light (sun)
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight);

        // Point light for additional illumination
        const pointLight = new THREE.PointLight(0xffffff, 0.5, 100);
        pointLight.position.set(0, 10, 0);
        this.scene.add(pointLight);
    }

    async generateMesh(depthData, imageData, resolution = 128, depthStrength = 1.0, environmentType = 'landscape', progressCallback) {
        const { depthMap, width, height, originalImage } = depthData;
        
        // Clear previous mesh
        if (this.generatedMesh) {
            this.scene.remove(this.generatedMesh);
        }

        // Create texture from original image
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = width;
        canvas.height = height;
        ctx.putImageData(originalImage, 0, 0);
        
        const texture = new THREE.CanvasTexture(canvas);
        texture.flipY = false;

        // Generate geometry based on environment type
        let geometry;
        switch (environmentType) {
            case 'landscape':
                geometry = this.generateLandscapeGeometry(depthMap, width, height, resolution, depthStrength, progressCallback);
                break;
            case 'room':
                geometry = this.generateRoomGeometry(depthMap, width, height, resolution, depthStrength, progressCallback);
                break;
            case 'terrain':
                geometry = this.generateTerrainGeometry(depthMap, width, height, resolution, depthStrength, progressCallback);
                break;
            default:
                geometry = this.generateLandscapeGeometry(depthMap, width, height, resolution, depthStrength, progressCallback);
        }

        // Create material
        const material = new THREE.MeshLambertMaterial({
            map: texture,
            side: THREE.DoubleSide
        });

        // Create mesh
        this.generatedMesh = new THREE.Mesh(geometry, material);
        this.generatedMesh.receiveShadow = true;
        this.generatedMesh.castShadow = true;
        
        this.scene.add(this.generatedMesh);

        // Add environment elements
        this.addEnvironmentElements(environmentType);

        if (progressCallback) {
            progressCallback(100);
        }

        return this.generatedMesh;
    }

    generateLandscapeGeometry(depthMap, width, height, resolution, depthStrength, progressCallback) {
        const geometry = new THREE.PlaneGeometry(20, 20, resolution - 1, resolution - 1);
        const vertices = geometry.attributes.position.array;
        const uvs = geometry.attributes.uv.array;

        // Modify vertices based on depth map
        for (let i = 0; i < vertices.length; i += 3) {
            const x = vertices[i];
            const z = vertices[i + 2];
            
            // Map 3D coordinates to image coordinates
            const imgX = Math.floor(((x + 10) / 20) * width);
            const imgY = Math.floor(((z + 10) / 20) * height);
            const depthIndex = Math.min(imgY * width + imgX, depthMap.length - 1);
            
            // Apply depth with strength multiplier
            vertices[i + 1] = depthMap[depthIndex] * depthStrength * 5;
            
            if (progressCallback && i % 1000 === 0) {
                const progress = 50 + (i / vertices.length) * 40; // 50-90%
                progressCallback(progress);
            }
        }

        geometry.attributes.position.needsUpdate = true;
        geometry.computeVertexNormals();
        
        return geometry;
    }

    generateRoomGeometry(depthMap, width, height, resolution, depthStrength, progressCallback) {
        // Create a room-like structure with walls
        const group = new THREE.Group();
        
        // Floor
        const floorGeometry = new THREE.PlaneGeometry(20, 20, resolution - 1, resolution - 1);
        floorGeometry.rotateX(-Math.PI / 2);
        
        // Back wall
        const wallGeometry = new THREE.PlaneGeometry(20, 10, resolution - 1, Math.floor(resolution / 2));
        wallGeometry.translate(0, 5, -10);
        
        // Apply depth mapping to floor
        const floorVertices = floorGeometry.attributes.position.array;
        for (let i = 0; i < floorVertices.length; i += 3) {
            const x = floorVertices[i];
            const z = floorVertices[i + 2];
            
            const imgX = Math.floor(((x + 10) / 20) * width);
            const imgY = Math.floor(((z + 10) / 20) * height);
            const depthIndex = Math.min(imgY * width + imgX, depthMap.length - 1);
            
            floorVertices[i + 1] = depthMap[depthIndex] * depthStrength * 2;
        }
        
        floorGeometry.attributes.position.needsUpdate = true;
        floorGeometry.computeVertexNormals();
        
        // Combine geometries
        const combinedGeometry = new THREE.BufferGeometry();
        const geometries = [floorGeometry, wallGeometry];
        combinedGeometry.copy(THREE.BufferGeometryUtils.mergeBufferGeometries(geometries));
        
        return combinedGeometry;
    }

    generateTerrainGeometry(depthMap, width, height, resolution, depthStrength, progressCallback) {
        const geometry = new THREE.PlaneGeometry(30, 30, resolution - 1, resolution - 1);
        const vertices = geometry.attributes.position.array;

        // Create more dramatic terrain variations
        for (let i = 0; i < vertices.length; i += 3) {
            const x = vertices[i];
            const z = vertices[i + 2];
            
            const imgX = Math.floor(((x + 15) / 30) * width);
            const imgY = Math.floor(((z + 15) / 30) * height);
            const depthIndex = Math.min(imgY * width + imgX, depthMap.length - 1);
            
            // Apply more dramatic height variations for terrain
            const baseHeight = depthMap[depthIndex] * depthStrength * 8;
            
            // Add some noise for more realistic terrain
            const noise = (Math.random() - 0.5) * 0.5;
            vertices[i + 1] = baseHeight + noise;
        }

        geometry.attributes.position.needsUpdate = true;
        geometry.computeVertexNormals();
        
        return geometry;
    }

    addEnvironmentElements(environmentType) {
        switch (environmentType) {
            case 'landscape':
                this.addSkybox();
                break;
            case 'room':
                this.addRoomLighting();
                break;
            case 'terrain':
                this.addTerrainElements();
                break;
        }
    }

    addSkybox() {
        const skyGeometry = new THREE.SphereGeometry(500, 32, 32);
        const skyMaterial = new THREE.MeshBasicMaterial({
            color: 0x87CEEB,
            side: THREE.BackSide
        });
        const sky = new THREE.Mesh(skyGeometry, skyMaterial);
        this.scene.add(sky);
    }

    addRoomLighting() {
        // Additional room-specific lighting
        const roomLight = new THREE.PointLight(0xffffff, 0.3, 50);
        roomLight.position.set(0, 8, 0);
        this.scene.add(roomLight);
    }

    addTerrainElements() {
        // Add some basic terrain elements like trees or rocks
        // This is a simplified version - you could add more complex elements
        for (let i = 0; i < 10; i++) {
            const treeGeometry = new THREE.ConeGeometry(0.5, 3, 8);
            const treeMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
            const tree = new THREE.Mesh(treeGeometry, treeMaterial);
            
            tree.position.set(
                (Math.random() - 0.5) * 20,
                1.5,
                (Math.random() - 0.5) * 20
            );
            
            this.scene.add(tree);
        }
    }

    animate() {
        requestAnimationFrame(() => this.animate());
        
        if (this.controls) {
            this.controls.update();
        }
        
        if (this.renderer && this.scene && this.camera) {
            this.renderer.render(this.scene, this.camera);
        }
    }

    resetView() {
        if (this.camera && this.controls) {
            this.camera.position.set(0, 5, 10);
            this.controls.reset();
        }
    }

    resize(width, height) {
        if (this.camera && this.renderer) {
            this.camera.aspect = width / height;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(width, height);
        }
    }
}

// Export for use in other modules
window.MeshGenerator = MeshGenerator;

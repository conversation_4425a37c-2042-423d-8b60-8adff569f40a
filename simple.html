<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>2D to 3D Environment Generator - Simple Version</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>2D Image to 3D Environment Generator</h1>
            <p>Upload a 2D image and convert it to a 3D environment exportable to Blender</p>
        </header>

        <main>
            <div class="upload-section">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-content">
                        <svg class="upload-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                            <polyline points="7,10 12,15 17,10"></polyline>
                            <line x1="12" y1="15" x2="12" y2="3"></line>
                        </svg>
                        <h3>Drop your image here or click to browse</h3>
                        <p>Supports JPG, PNG, WebP formats</p>
                    </div>
                    <input type="file" id="imageInput" accept="image/*" hidden>
                </div>
                
                <div class="image-preview" id="imagePreview" style="display: none;">
                    <img id="previewImg" alt="Preview">
                    <button id="removeImage" class="remove-btn">×</button>
                </div>
            </div>

            <div class="controls-section">
                <div class="control-group">
                    <label for="depthStrength">Depth Strength:</label>
                    <input type="range" id="depthStrength" min="0.1" max="2.0" step="0.1" value="1.0">
                    <span id="depthValue">1.0</span>
                </div>
                
                <div class="control-group">
                    <label for="meshResolution">Mesh Resolution:</label>
                    <select id="meshResolution">
                        <option value="64">Low (64x64)</option>
                        <option value="128" selected>Medium (128x128)</option>
                        <option value="256">High (256x256)</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label for="environmentType">Environment Type:</label>
                    <select id="environmentType">
                        <option value="landscape">Landscape</option>
                        <option value="room">Indoor Room</option>
                        <option value="terrain">Terrain</option>
                    </select>
                </div>
                
                <button id="generateBtn" class="generate-btn" disabled>Generate 3D Environment</button>
            </div>

            <div class="viewer-section">
                <div class="viewer-container">
                    <canvas id="threejsCanvas"></canvas>
                    <div class="viewer-controls">
                        <button id="resetView">Reset View</button>
                        <button id="exportGLB" disabled>Export GLB</button>
                    </div>
                </div>
                
                <div class="progress-section" id="progressSection" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <p id="progressText">Processing...</p>
                </div>
            </div>

            <div id="debugLog" style="background: #f0f0f0; padding: 10px; margin: 20px 0; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;"></div>
        </main>

        <footer>
            <p>Built with Three.js and TensorFlow.js</p>
        </footer>
    </div>

    <!-- Load dependencies with error handling -->
    <script>
        function log(message) {
            const debugLog = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            debugLog.innerHTML += `[${timestamp}] ${message}<br>`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }
        
        log('Starting to load dependencies...');
    </script>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js" 
            onload="log('✓ Three.js loaded')" 
            onerror="log('✗ Failed to load Three.js')"></script>
    <script src="https://threejs.org/examples/js/controls/OrbitControls.js" 
            onload="log('✓ OrbitControls loaded')" 
            onerror="log('✗ Failed to load OrbitControls')"></script>
    <script src="https://threejs.org/examples/js/exporters/GLTFExporter.js" 
            onload="log('✓ GLTFExporter loaded')" 
            onerror="log('✗ Failed to load GLTFExporter')"></script>
    <script src="https://threejs.org/examples/js/utils/BufferGeometryUtils.js" 
            onload="log('✓ BufferGeometryUtils loaded')" 
            onerror="log('✗ Failed to load BufferGeometryUtils')"></script>

    <!-- Load our modules -->
    <script src="depthEstimation.js" onload="log('✓ DepthEstimation loaded')" onerror="log('✗ Failed to load DepthEstimation')"></script>
    <script src="meshGenerator.js" onload="log('✓ MeshGenerator loaded')" onerror="log('✗ Failed to load MeshGenerator')"></script>
    <script src="glbExporter.js" onload="log('✓ GLBExporter loaded')" onerror="log('✗ Failed to load GLBExporter')"></script>

    <!-- Complete functionality -->
    <script>
        let currentImage = null;
        let depthEstimator = null;
        let meshGenerator = null;
        let glbExporter = null;

        document.addEventListener('DOMContentLoaded', function() {
            log('DOM loaded, initializing simple upload...');
            
            const uploadArea = document.getElementById('uploadArea');
            const imageInput = document.getElementById('imageInput');
            const imagePreview = document.getElementById('imagePreview');
            const previewImg = document.getElementById('previewImg');
            const removeImageBtn = document.getElementById('removeImage');
            const generateBtn = document.getElementById('generateBtn');
            
            if (!uploadArea || !imageInput) {
                log('✗ Upload elements not found!');
                return;
            }
            
            log('✓ Upload elements found');
            
            // Click to upload
            uploadArea.addEventListener('click', function() {
                log('Upload area clicked');
                imageInput.click();
            });
            
            // File input change
            imageInput.addEventListener('change', function(e) {
                log('File input changed');
                const file = e.target.files[0];
                if (file) {
                    log(`File selected: ${file.name} (${file.type})`);
                    loadImage(file);
                } else {
                    log('No file selected');
                }
            });
            
            // Remove image
            removeImageBtn.addEventListener('click', function() {
                log('Remove image clicked');
                uploadArea.style.display = 'block';
                imagePreview.style.display = 'none';
                imageInput.value = '';
                generateBtn.disabled = true;
            });
            
            // Drag and drop
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                e.stopPropagation();
                uploadArea.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                e.stopPropagation();
                if (!uploadArea.contains(e.relatedTarget)) {
                    uploadArea.classList.remove('dragover');
                }
            });
            
            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                e.stopPropagation();
                uploadArea.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                log(`Files dropped: ${files.length}`);
                
                if (files.length > 0) {
                    const file = files[0];
                    log(`Processing dropped file: ${file.name}`);
                    loadImage(file);
                }
            });
            

            
            // Initialize 3D modules
            setTimeout(function() {
                try {
                    if (typeof DepthEstimator !== 'undefined') {
                        depthEstimator = new DepthEstimator();
                        log('✓ DepthEstimator initialized');
                    }
                    if (typeof MeshGenerator !== 'undefined') {
                        meshGenerator = new MeshGenerator();
                        const canvas = document.getElementById('threejsCanvas');
                        if (canvas) {
                            meshGenerator.initializeThreeJS(canvas);
                            log('✓ MeshGenerator initialized');
                        }
                    }
                    if (typeof GLBExporter !== 'undefined') {
                        glbExporter = new GLBExporter();
                        log('✓ GLBExporter initialized');
                    }
                } catch (error) {
                    log('✗ Error initializing 3D modules: ' + error.message);
                }
            }, 1000);

            // Generate button functionality
            generateBtn.addEventListener('click', function() {
                if (!currentImage) {
                    log('✗ No image loaded');
                    alert('Please upload an image first.');
                    return;
                }

                log('🚀 Starting 3D generation...');
                generate3DEnvironment();
            });

            // Depth strength slider
            const depthStrength = document.getElementById('depthStrength');
            const depthValue = document.getElementById('depthValue');
            if (depthStrength && depthValue) {
                depthStrength.addEventListener('input', function(e) {
                    depthValue.textContent = e.target.value;
                });
            }

            // Export GLB button
            const exportGLBBtn = document.getElementById('exportGLB');
            if (exportGLBBtn) {
                exportGLBBtn.addEventListener('click', function() {
                    if (!meshGenerator || !meshGenerator.generatedMesh) {
                        log('✗ No 3D environment generated');
                        alert('Please generate a 3D environment first.');
                        return;
                    }

                    log('📦 Exporting GLB...');
                    exportGLB();
                });
            }

            // Reset view button
            const resetViewBtn = document.getElementById('resetView');
            if (resetViewBtn) {
                resetViewBtn.addEventListener('click', function() {
                    if (meshGenerator) {
                        meshGenerator.resetView();
                        log('🔄 View reset');
                    }
                });
            }

            log('✓ Complete functionality initialized');
        });

        function loadImage(file) {
            log(`Loading image: ${file.name}`);

            if (!file.type.startsWith('image/')) {
                log('✗ Not an image file');
                alert('Please select a valid image file.');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                log('✓ File read successfully');
                const img = new Image();
                img.onload = function() {
                    log(`✓ Image loaded: ${img.width}x${img.height}`);

                    // Store the current image
                    currentImage = img;

                    // Show preview
                    const previewImg = document.getElementById('previewImg');
                    const uploadArea = document.getElementById('uploadArea');
                    const imagePreview = document.getElementById('imagePreview');
                    const generateBtn = document.getElementById('generateBtn');

                    previewImg.src = e.target.result;
                    uploadArea.style.display = 'none';
                    imagePreview.style.display = 'block';
                    generateBtn.disabled = false;

                    log('✓ Image preview displayed and ready for 3D generation');
                };
                img.onerror = function() {
                    log('✗ Failed to load image');
                    alert('Failed to load the image.');
                };
                img.src = e.target.result;
            };
            reader.onerror = function() {
                log('✗ Failed to read file');
                alert('Failed to read the file.');
            };
            reader.readAsDataURL(file);
        }

        async function generate3DEnvironment() {
            if (!currentImage || !depthEstimator || !meshGenerator) {
                log('✗ Missing required components');
                return;
            }

            try {
                // Show progress
                const progressSection = document.getElementById('progressSection');
                const progressFill = document.getElementById('progressFill');
                const progressText = document.getElementById('progressText');

                progressSection.style.display = 'block';
                updateProgress(10, 'Analyzing image...');

                // Get settings
                const depthStrength = parseFloat(document.getElementById('depthStrength').value);
                const meshResolution = parseInt(document.getElementById('meshResolution').value);
                const environmentType = document.getElementById('environmentType').value;

                log(`Settings: depth=${depthStrength}, resolution=${meshResolution}, type=${environmentType}`);

                // Estimate depth
                updateProgress(30, 'Estimating depth...');
                const depthData = await depthEstimator.enhancedDepthEstimation(currentImage, environmentType);
                log('✓ Depth estimation complete');

                // Generate mesh
                updateProgress(60, 'Generating 3D mesh...');
                await meshGenerator.generateMesh(
                    depthData,
                    currentImage,
                    meshResolution,
                    depthStrength,
                    environmentType,
                    (progress) => updateProgress(60 + progress * 0.3, 'Creating 3D environment...')
                );

                updateProgress(100, 'Complete!');
                log('✓ 3D environment generated successfully');

                // Enable export
                const exportGLBBtn = document.getElementById('exportGLB');
                if (exportGLBBtn) {
                    exportGLBBtn.disabled = false;
                }

                setTimeout(() => {
                    progressSection.style.display = 'none';
                }, 2000);

            } catch (error) {
                log('✗ Error generating 3D environment: ' + error.message);
                console.error('Generation error:', error);
                alert('Error generating 3D environment: ' + error.message);

                const progressSection = document.getElementById('progressSection');
                progressSection.style.display = 'none';
            }
        }

        async function exportGLB() {
            if (!glbExporter || !meshGenerator || !meshGenerator.generatedMesh) {
                log('✗ Cannot export: missing components');
                return;
            }

            try {
                const progressSection = document.getElementById('progressSection');
                progressSection.style.display = 'block';
                updateProgress(50, 'Preparing GLB export...');

                const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
                const filename = `3d_environment_${timestamp}`;

                await glbExporter.exportMesh(meshGenerator.generatedMesh, filename);

                updateProgress(100, 'Export complete!');
                log('✓ GLB export successful: ' + filename + '.glb');

                // Show export statistics
                const stats = glbExporter.getExportStats(meshGenerator.scene);
                const message = `Export successful!\n\nFile: ${filename}.glb\nVertices: ${stats.vertices}\nFaces: ${stats.faces}\nMaterials: ${stats.materials}\nTextures: ${stats.textures}`;
                alert(message);

                setTimeout(() => {
                    progressSection.style.display = 'none';
                }, 2000);

            } catch (error) {
                log('✗ Export error: ' + error.message);
                console.error('Export error:', error);
                alert('Error exporting GLB: ' + error.message);

                const progressSection = document.getElementById('progressSection');
                progressSection.style.display = 'none';
            }
        }

        function updateProgress(percentage, text) {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');

            if (progressFill) progressFill.style.width = `${percentage}%`;
            if (progressText) progressText.textContent = text;

            log(`Progress: ${percentage}% - ${text}`);
        }
    </script>
</body>
</html>

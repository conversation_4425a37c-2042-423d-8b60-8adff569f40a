#!/usr/bin/env python3
"""
Simple HTTP server to serve the 2D to 3D Environment Generator
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

def start_server(port=8000):
    """Start a simple HTTP server"""
    
    # Change to the directory containing this script
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # Create server
    handler = http.server.SimpleHTTPRequestHandler
    
    try:
        with socketserver.TCPServer(("", port), handler) as httpd:
            print(f"🚀 Starting server at http://localhost:{port}")
            print(f"📁 Serving files from: {script_dir}")
            print(f"🌐 Main application: http://localhost:{port}/index.html")
            print(f"🧪 Test page: http://localhost:{port}/test.html")
            print("\n📋 Instructions:")
            print("1. Open http://localhost:{port}/index.html in your browser")
            print("2. Upload a 2D image (JPG, PNG, WebP)")
            print("3. Adjust settings and generate 3D environment")
            print("4. Export as GLB file for Blender")
            print("\nPress Ctrl+C to stop the server")
            
            # Try to open browser automatically
            try:
                webbrowser.open(f"http://localhost:{port}/index.html")
            except:
                pass
            
            # Start serving
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ Port {port} is already in use. Try a different port:")
            print(f"   python start_server.py {port + 1}")
        else:
            print(f"❌ Error starting server: {e}")

if __name__ == "__main__":
    port = 8000
    
    # Check if port is provided as command line argument
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print("❌ Invalid port number. Using default port 8000.")
            port = 8000
    
    start_server(port)

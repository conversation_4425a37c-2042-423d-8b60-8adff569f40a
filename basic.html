<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Basic 2D to 3D Converter</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .upload-section {
            border: 3px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background: #fafafa;
            margin: 20px 0;
        }
        .file-input {
            font-size: 16px;
            padding: 10px;
            margin: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            background: white;
        }
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
        }
        .btn:hover {
            background: #45a049;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .preview {
            text-align: center;
            margin: 20px 0;
        }
        .preview img {
            max-width: 400px;
            max-height: 300px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .controls {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .control-group {
            margin: 15px 0;
        }
        .control-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .control-group input, .control-group select {
            width: 100%;
            padding: 8px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .canvas-container {
            text-align: center;
            margin: 20px 0;
        }
        #canvas3d {
            border: 2px solid #ddd;
            border-radius: 8px;
            background: #000;
        }
        .log {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 20px 0;
            border: 1px solid #ddd;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 2D to 3D Environment Generator</h1>
        
        <div class="upload-section">
            <h3>📁 Upload Your Image</h3>
            <p>Select a JPG, PNG, or WebP image to convert to 3D</p>
            <input type="file" id="imageFile" class="file-input" accept="image/*">
            <br>
            <button onclick="loadSelectedImage()" class="btn">Load Image</button>
        </div>
        
        <div id="imagePreview" class="preview" style="display: none;">
            <h3>📷 Image Preview</h3>
            <img id="previewImg" alt="Preview">
            <br>
            <button onclick="clearImage()" class="btn" style="background: #f44336;">Remove Image</button>
        </div>
        
        <div id="controls" class="controls" style="display: none;">
            <h3>⚙️ 3D Generation Settings</h3>
            
            <div class="control-group">
                <label for="depthStrength">Depth Strength (0.1 - 3.0):</label>
                <input type="range" id="depthStrength" min="0.1" max="3.0" step="0.1" value="1.5">
                <span id="depthValue">1.5</span>
            </div>
            
            <div class="control-group">
                <label for="meshResolution">Mesh Quality:</label>
                <select id="meshResolution">
                    <option value="32">Low Quality (32x32) - Fast</option>
                    <option value="64" selected>Medium Quality (64x64) - Balanced</option>
                    <option value="128">High Quality (128x128) - Detailed</option>
                </select>
            </div>
            
            <div class="control-group">
                <label for="environmentType">Environment Type:</label>
                <select id="environmentType">
                    <option value="landscape">🏞️ Landscape (Outdoor scenes)</option>
                    <option value="room">🏠 Indoor Room (Interior spaces)</option>
                    <option value="terrain">🏔️ Terrain (Heightmap style)</option>
                </select>
            </div>
            
            <button onclick="generate3DEnvironment()" class="btn" id="generateBtn">🚀 Generate 3D Environment</button>
        </div>
        
        <div id="status"></div>
        
        <div class="canvas-container">
            <canvas id="canvas3d" width="600" height="400" style="display: none;"></canvas>
        </div>
        
        <div id="exportSection" style="display: none; text-align: center;">
            <button onclick="exportToGLB()" class="btn" style="background: #2196F3;">📦 Export GLB for Blender</button>
            <p>The GLB file will be downloaded and can be imported directly into Blender</p>
        </div>
        
        <div class="log" id="log">
            <strong>📋 Activity Log:</strong><br>
            [Ready] Application loaded successfully<br>
        </div>
    </div>

    <!-- Three.js Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://threejs.org/examples/js/controls/OrbitControls.js"></script>
    <script src="https://threejs.org/examples/js/exporters/GLTFExporter.js"></script>

    <script>
        // Global variables
        let currentImage = null;
        let scene, camera, renderer, controls, generatedMesh;
        
        // Logging function
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        // Status display function
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
            if (type !== 'error') {
                setTimeout(() => statusDiv.innerHTML = '', 3000);
            }
        }
        
        // Load selected image
        function loadSelectedImage() {
            const fileInput = document.getElementById('imageFile');
            const file = fileInput.files[0];
            
            if (!file) {
                showStatus('❌ Please select an image file first', 'error');
                log('ERROR: No file selected');
                return;
            }
            
            if (!file.type.startsWith('image/')) {
                showStatus('❌ Please select a valid image file (JPG, PNG, WebP)', 'error');
                log('ERROR: Invalid file type: ' + file.type);
                return;
            }
            
            log(`Loading image: ${file.name} (${(file.size/1024).toFixed(1)}KB)`);
            showStatus('📖 Loading image...', 'info');
            
            const reader = new FileReader();
            
            reader.onload = function(e) {
                const img = new Image();
                
                img.onload = function() {
                    currentImage = img;
                    
                    // Show preview
                    document.getElementById('previewImg').src = e.target.result;
                    document.getElementById('imagePreview').style.display = 'block';
                    document.getElementById('controls').style.display = 'block';
                    
                    log(`✅ Image loaded successfully: ${img.width}x${img.height}`);
                    showStatus('✅ Image loaded! Configure settings and generate 3D environment', 'success');
                };
                
                img.onerror = function() {
                    log('ERROR: Failed to load image');
                    showStatus('❌ Failed to load image', 'error');
                };
                
                img.src = e.target.result;
            };
            
            reader.onerror = function() {
                log('ERROR: Failed to read file');
                showStatus('❌ Failed to read file', 'error');
            };
            
            reader.readAsDataURL(file);
        }
        
        // Clear image
        function clearImage() {
            currentImage = null;
            document.getElementById('imageFile').value = '';
            document.getElementById('imagePreview').style.display = 'none';
            document.getElementById('controls').style.display = 'none';
            document.getElementById('canvas3d').style.display = 'none';
            document.getElementById('exportSection').style.display = 'none';
            
            log('Image cleared');
            showStatus('🗑️ Image removed', 'info');
        }
        
        // Update depth value display
        document.getElementById('depthStrength').addEventListener('input', function(e) {
            document.getElementById('depthValue').textContent = e.target.value;
        });
        
        // Generate 3D Environment
        function generate3DEnvironment() {
            if (!currentImage) {
                showStatus('❌ Please load an image first', 'error');
                return;
            }
            
            log('🚀 Starting 3D environment generation...');
            showStatus('🔄 Generating 3D environment...', 'info');
            
            try {
                // Initialize Three.js if needed
                if (!scene) {
                    initializeThreeJS();
                }
                
                // Get settings
                const depthStrength = parseFloat(document.getElementById('depthStrength').value);
                const resolution = parseInt(document.getElementById('meshResolution').value);
                const environmentType = document.getElementById('environmentType').value;
                
                log(`Settings: Depth=${depthStrength}, Resolution=${resolution}x${resolution}, Type=${environmentType}`);
                
                // Generate depth map
                log('📊 Creating depth map from image...');
                const depthData = createDepthMap(currentImage);
                
                // Create 3D mesh
                log('🎯 Generating 3D mesh...');
                create3DMesh(depthData, resolution, depthStrength);
                
                // Show result
                document.getElementById('canvas3d').style.display = 'block';
                document.getElementById('exportSection').style.display = 'block';
                
                log('✅ 3D environment generated successfully!');
                showStatus('🎉 3D environment ready! Use mouse to explore. Click Export GLB for Blender.', 'success');
                
            } catch (error) {
                log('ERROR: ' + error.message);
                console.error(error);
                showStatus('❌ Error generating 3D environment: ' + error.message, 'error');
            }
        }
        
        // Initialize Three.js
        function initializeThreeJS() {
            log('🎮 Initializing 3D engine...');
            
            const canvas = document.getElementById('canvas3d');
            
            // Scene
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x87CEEB);
            
            // Camera
            camera = new THREE.PerspectiveCamera(75, canvas.width / canvas.height, 0.1, 1000);
            camera.position.set(0, 8, 15);
            
            // Renderer
            renderer = new THREE.WebGLRenderer({ canvas: canvas, antialias: true });
            renderer.setSize(canvas.width, canvas.height);
            renderer.shadowMap.enabled = true;
            
            // Controls
            controls = new THREE.OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;
            
            // Lighting
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(10, 10, 5);
            directionalLight.castShadow = true;
            scene.add(directionalLight);
            
            // Start animation loop
            animate();
            
            log('✅ 3D engine initialized');
        }
        
        // Animation loop
        function animate() {
            requestAnimationFrame(animate);
            if (controls) controls.update();
            if (renderer && scene && camera) renderer.render(scene, camera);
        }
        
        // Create depth map from image
        function createDepthMap(img) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = img.naturalWidth;
            canvas.height = img.naturalHeight;
            ctx.drawImage(img, 0, 0);
            
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;
            const depthMap = new Float32Array(canvas.width * canvas.height);
            
            // Create depth map using luminance
            for (let i = 0; i < data.length; i += 4) {
                const r = data[i];
                const g = data[i + 1];
                const b = data[i + 2];
                
                // Calculate luminance (brightness)
                const luminance = 0.299 * r + 0.587 * g + 0.114 * b;
                depthMap[i / 4] = luminance / 255;
            }
            
            return {
                depthMap: depthMap,
                width: canvas.width,
                height: canvas.height,
                texture: new THREE.CanvasTexture(canvas)
            };
        }
        
        // Create 3D mesh
        function create3DMesh(depthData, resolution, depthStrength) {
            // Remove existing mesh
            if (generatedMesh) {
                scene.remove(generatedMesh);
            }
            
            // Create plane geometry
            const geometry = new THREE.PlaneGeometry(25, 25, resolution - 1, resolution - 1);
            const vertices = geometry.attributes.position.array;
            
            // Apply depth displacement
            for (let i = 0; i < vertices.length; i += 3) {
                const x = vertices[i];
                const z = vertices[i + 2];
                
                // Map to image coordinates
                const imgX = Math.floor(((x + 12.5) / 25) * depthData.width);
                const imgY = Math.floor(((z + 12.5) / 25) * depthData.height);
                const index = Math.min(imgY * depthData.width + imgX, depthData.depthMap.length - 1);
                
                // Apply depth with strength multiplier
                vertices[i + 1] = depthData.depthMap[index] * depthStrength * 4;
            }
            
            geometry.attributes.position.needsUpdate = true;
            geometry.computeVertexNormals();
            
            // Create material with image texture
            const material = new THREE.MeshLambertMaterial({
                map: depthData.texture,
                side: THREE.DoubleSide
            });
            
            // Create and add mesh
            generatedMesh = new THREE.Mesh(geometry, material);
            scene.add(generatedMesh);
        }
        
        // Export to GLB
        function exportToGLB() {
            if (!generatedMesh) {
                showStatus('❌ No 3D environment to export', 'error');
                return;
            }
            
            log('📦 Exporting GLB file...');
            showStatus('📦 Preparing GLB export...', 'info');
            
            const exporter = new THREE.GLTFExporter();
            
            exporter.parse(scene, function(result) {
                // Create download
                const blob = new Blob([result], { type: 'application/octet-stream' });
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                
                const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
                const filename = `3d_environment_${timestamp}.glb`;
                
                link.href = url;
                link.download = filename;
                link.click();
                
                URL.revokeObjectURL(url);
                
                log(`✅ GLB exported: ${filename}`);
                showStatus(`🎉 GLB file downloaded: ${filename}`, 'success');
                
            }, { binary: true, embedImages: true });
        }
        
        log('🎯 Application ready - Select an image to begin!');
    </script>
</body>
</html>

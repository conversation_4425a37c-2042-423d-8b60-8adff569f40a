class GLBExporter {
    constructor() {
        this.exporter = null;
        this.initializeExporter();
    }

    initializeExporter() {
        // Initialize the GLTF exporter
        this.exporter = new THREE.GLTFExporter();
    }

    async exportScene(scene, filename = 'generated_environment') {
        return new Promise((resolve, reject) => {
            if (!this.exporter) {
                reject(new Error('GLB Exporter not initialized'));
                return;
            }

            // Configure export options
            const options = {
                binary: true, // Export as GLB (binary format)
                embedImages: true, // Embed textures
                animations: [], // No animations for now
                onlyVisible: true, // Only export visible objects
                truncateDrawRange: true,
                maxTextureSize: 2048, // Limit texture size for compatibility
                forcePowerOfTwoTextures: false
            };

            // Export the scene
            this.exporter.parse(
                scene,
                (result) => {
                    try {
                        // Create blob and download
                        const blob = new Blob([result], { type: 'application/octet-stream' });
                        this.downloadBlob(blob, `${filename}.glb`);
                        resolve(blob);
                    } catch (error) {
                        reject(error);
                    }
                },
                (error) => {
                    reject(error);
                },
                options
            );
        });
    }

    async exportMesh(mesh, filename = 'generated_mesh') {
        return new Promise((resolve, reject) => {
            if (!this.exporter) {
                reject(new Error('GLB Exporter not initialized'));
                return;
            }

            // Create a temporary scene with just the mesh
            const tempScene = new THREE.Scene();
            
            // Clone the mesh to avoid modifying the original
            const clonedMesh = mesh.clone();
            clonedMesh.material = mesh.material.clone();
            
            // Ensure the mesh has proper materials and textures
            this.prepareMeshForExport(clonedMesh);
            
            tempScene.add(clonedMesh);

            // Add basic lighting to the exported scene
            const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(10, 10, 5);
            
            tempScene.add(ambientLight);
            tempScene.add(directionalLight);

            const options = {
                binary: true,
                embedImages: true,
                animations: [],
                onlyVisible: true,
                truncateDrawRange: true,
                maxTextureSize: 2048,
                forcePowerOfTwoTextures: false
            };

            this.exporter.parse(
                tempScene,
                (result) => {
                    try {
                        const blob = new Blob([result], { type: 'application/octet-stream' });
                        this.downloadBlob(blob, `${filename}.glb`);
                        resolve(blob);
                    } catch (error) {
                        reject(error);
                    }
                },
                (error) => {
                    reject(error);
                },
                options
            );
        });
    }

    prepareMeshForExport(mesh) {
        // Ensure the mesh has proper UVs
        if (!mesh.geometry.attributes.uv) {
            this.generateUVs(mesh.geometry);
        }

        // Ensure the mesh has proper normals
        if (!mesh.geometry.attributes.normal) {
            mesh.geometry.computeVertexNormals();
        }

        // Optimize the material for export
        if (mesh.material) {
            // Convert to standard material if needed
            if (mesh.material.type !== 'MeshStandardMaterial' && mesh.material.type !== 'MeshBasicMaterial') {
                const standardMaterial = new THREE.MeshStandardMaterial();
                
                // Copy relevant properties
                if (mesh.material.map) {
                    standardMaterial.map = mesh.material.map;
                }
                if (mesh.material.color) {
                    standardMaterial.color = mesh.material.color;
                }
                
                mesh.material = standardMaterial;
            }

            // Ensure textures are properly configured
            if (mesh.material.map) {
                mesh.material.map.flipY = false;
                mesh.material.map.wrapS = THREE.ClampToEdgeWrapping;
                mesh.material.map.wrapT = THREE.ClampToEdgeWrapping;
            }
        }

        // Ensure geometry is properly indexed
        if (!mesh.geometry.index) {
            mesh.geometry = mesh.geometry.toNonIndexed();
        }
    }

    generateUVs(geometry) {
        // Simple planar UV mapping
        const positions = geometry.attributes.position.array;
        const uvs = new Float32Array(positions.length / 3 * 2);

        // Find bounding box
        let minX = Infinity, maxX = -Infinity;
        let minZ = Infinity, maxZ = -Infinity;

        for (let i = 0; i < positions.length; i += 3) {
            const x = positions[i];
            const z = positions[i + 2];
            
            minX = Math.min(minX, x);
            maxX = Math.max(maxX, x);
            minZ = Math.min(minZ, z);
            maxZ = Math.max(maxZ, z);
        }

        const rangeX = maxX - minX;
        const rangeZ = maxZ - minZ;

        // Generate UVs
        for (let i = 0, j = 0; i < positions.length; i += 3, j += 2) {
            const x = positions[i];
            const z = positions[i + 2];
            
            uvs[j] = (x - minX) / rangeX;
            uvs[j + 1] = (z - minZ) / rangeZ;
        }

        geometry.setAttribute('uv', new THREE.BufferAttribute(uvs, 2));
    }

    downloadBlob(blob, filename) {
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // Clean up the URL object
        setTimeout(() => {
            URL.revokeObjectURL(url);
        }, 100);
    }

    // Utility method to validate GLB file
    validateGLB(arrayBuffer) {
        try {
            // Check GLB header
            const view = new DataView(arrayBuffer);
            const magic = view.getUint32(0, true);
            const version = view.getUint32(4, true);
            const length = view.getUint32(8, true);

            // GLB magic number is 0x46546C67 ("glTF" in ASCII)
            if (magic !== 0x46546C67) {
                return { valid: false, error: 'Invalid GLB magic number' };
            }

            if (version !== 2) {
                return { valid: false, error: 'Unsupported GLB version' };
            }

            if (length !== arrayBuffer.byteLength) {
                return { valid: false, error: 'GLB length mismatch' };
            }

            return { valid: true };
        } catch (error) {
            return { valid: false, error: error.message };
        }
    }

    // Method to get export statistics
    getExportStats(scene) {
        let vertexCount = 0;
        let faceCount = 0;
        let materialCount = 0;
        let textureCount = 0;

        scene.traverse((object) => {
            if (object.isMesh) {
                const geometry = object.geometry;
                if (geometry.attributes.position) {
                    vertexCount += geometry.attributes.position.count;
                }
                if (geometry.index) {
                    faceCount += geometry.index.count / 3;
                } else {
                    faceCount += geometry.attributes.position.count / 3;
                }
                
                if (object.material) {
                    materialCount++;
                    if (object.material.map) {
                        textureCount++;
                    }
                }
            }
        });

        return {
            vertices: vertexCount,
            faces: Math.floor(faceCount),
            materials: materialCount,
            textures: textureCount
        };
    }
}

// Export for use in other modules
window.GLBExporter = GLBExporter;

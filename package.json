{"name": "image-to-3d-environment", "version": "1.0.0", "description": "A web application that converts 2D images to 3D environments and exports them as GLB files for Blender", "main": "index.html", "scripts": {"start": "python -m http.server 8000", "serve": "npx http-server -p 8000 -c-1", "dev": "npx live-server --port=8000"}, "keywords": ["3d", "image-processing", "threejs", "glb", "blender", "depth-estimation", "mesh-generation"], "author": "Your Name", "license": "MIT", "devDependencies": {"http-server": "^14.1.1", "live-server": "^1.2.2"}, "dependencies": {}, "repository": {"type": "git", "url": "https://github.com/yourusername/image-to-3d-environment.git"}, "bugs": {"url": "https://github.com/yourusername/image-to-3d-environment/issues"}, "homepage": "https://github.com/yourusername/image-to-3d-environment#readme"}
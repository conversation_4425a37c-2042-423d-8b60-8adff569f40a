<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - 2D to 3D Environment Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid #ddd;
        }
        .test-pass {
            border-left-color: #4CAF50;
            background: #f1f8e9;
        }
        .test-fail {
            border-left-color: #f44336;
            background: #ffebee;
        }
        .test-pending {
            border-left-color: #ff9800;
            background: #fff3e0;
        }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976D2;
        }
        #testCanvas {
            border: 1px solid #ddd;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>2D to 3D Environment Generator - Test Suite</h1>
        
        <div id="testResults">
            <div class="test-item test-pending" id="test-three">
                <strong>Three.js Loading:</strong> <span id="three-status">Testing...</span>
            </div>
            <div class="test-item test-pending" id="test-controls">
                <strong>OrbitControls:</strong> <span id="controls-status">Testing...</span>
            </div>
            <div class="test-item test-pending" id="test-exporter">
                <strong>GLTFExporter:</strong> <span id="exporter-status">Testing...</span>
            </div>
            <div class="test-item test-pending" id="test-webgl">
                <strong>WebGL Support:</strong> <span id="webgl-status">Testing...</span>
            </div>
            <div class="test-item test-pending" id="test-modules">
                <strong>Custom Modules:</strong> <span id="modules-status">Testing...</span>
            </div>
        </div>

        <div style="margin: 20px 0;">
            <button onclick="runTests()">Run Tests</button>
            <button onclick="testBasic3D()">Test Basic 3D Scene</button>
            <button onclick="testDepthEstimation()">Test Depth Estimation</button>
        </div>

        <canvas id="testCanvas" width="400" height="300"></canvas>
        
        <div id="testOutput" style="margin-top: 20px; padding: 10px; background: #f5f5f5; border-radius: 4px; font-family: monospace; white-space: pre-wrap;"></div>
    </div>

    <!-- Load dependencies -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://threejs.org/examples/js/controls/OrbitControls.js"></script>
    <script src="https://threejs.org/examples/js/exporters/GLTFExporter.js"></script>
    <script src="https://threejs.org/examples/js/utils/BufferGeometryUtils.js"></script>

    <!-- Load our modules -->
    <script src="depthEstimation.js"></script>
    <script src="meshGenerator.js"></script>
    <script src="glbExporter.js"></script>

    <script>
        function updateTestStatus(testId, status, message) {
            const element = document.getElementById(testId);
            const statusElement = document.getElementById(testId.replace('test-', '') + '-status');
            
            element.className = `test-item test-${status}`;
            statusElement.textContent = message;
        }

        function log(message) {
            const output = document.getElementById('testOutput');
            output.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            console.log(message);
        }

        function runTests() {
            log('Starting test suite...');
            
            // Test Three.js
            try {
                if (typeof THREE !== 'undefined') {
                    updateTestStatus('test-three', 'pass', 'Three.js loaded successfully');
                    log('✓ Three.js is available');
                } else {
                    throw new Error('Three.js not found');
                }
            } catch (error) {
                updateTestStatus('test-three', 'fail', 'Failed to load Three.js');
                log('✗ Three.js failed: ' + error.message);
            }

            // Test OrbitControls
            try {
                if (typeof THREE.OrbitControls !== 'undefined') {
                    updateTestStatus('test-controls', 'pass', 'OrbitControls loaded successfully');
                    log('✓ OrbitControls is available');
                } else {
                    throw new Error('OrbitControls not found');
                }
            } catch (error) {
                updateTestStatus('test-controls', 'fail', 'Failed to load OrbitControls');
                log('✗ OrbitControls failed: ' + error.message);
            }

            // Test GLTFExporter
            try {
                if (typeof THREE.GLTFExporter !== 'undefined') {
                    updateTestStatus('test-exporter', 'pass', 'GLTFExporter loaded successfully');
                    log('✓ GLTFExporter is available');
                } else {
                    throw new Error('GLTFExporter not found');
                }
            } catch (error) {
                updateTestStatus('test-exporter', 'fail', 'Failed to load GLTFExporter');
                log('✗ GLTFExporter failed: ' + error.message);
            }

            // Test WebGL
            try {
                const canvas = document.createElement('canvas');
                const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                if (gl) {
                    updateTestStatus('test-webgl', 'pass', 'WebGL is supported');
                    log('✓ WebGL is supported');
                } else {
                    throw new Error('WebGL not supported');
                }
            } catch (error) {
                updateTestStatus('test-webgl', 'fail', 'WebGL not supported');
                log('✗ WebGL failed: ' + error.message);
            }

            // Test custom modules
            try {
                if (typeof DepthEstimator !== 'undefined' && 
                    typeof MeshGenerator !== 'undefined' && 
                    typeof GLBExporter !== 'undefined') {
                    updateTestStatus('test-modules', 'pass', 'All custom modules loaded');
                    log('✓ Custom modules are available');
                } else {
                    throw new Error('Some custom modules missing');
                }
            } catch (error) {
                updateTestStatus('test-modules', 'fail', 'Custom modules failed to load');
                log('✗ Custom modules failed: ' + error.message);
            }

            log('Test suite completed.');
        }

        function testBasic3D() {
            log('Testing basic 3D scene creation...');
            
            try {
                const canvas = document.getElementById('testCanvas');
                const scene = new THREE.Scene();
                const camera = new THREE.PerspectiveCamera(75, canvas.width / canvas.height, 0.1, 1000);
                const renderer = new THREE.WebGLRenderer({ canvas: canvas });
                
                renderer.setSize(canvas.width, canvas.height);
                
                // Create a simple cube
                const geometry = new THREE.BoxGeometry();
                const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
                const cube = new THREE.Mesh(geometry, material);
                scene.add(cube);
                
                camera.position.z = 5;
                
                // Render
                renderer.render(scene, camera);
                
                log('✓ Basic 3D scene created successfully');
            } catch (error) {
                log('✗ Basic 3D scene failed: ' + error.message);
            }
        }

        function testDepthEstimation() {
            log('Testing depth estimation...');
            
            try {
                const depthEstimator = new DepthEstimator();
                
                // Create a test image
                const canvas = document.createElement('canvas');
                canvas.width = 100;
                canvas.height = 100;
                const ctx = canvas.getContext('2d');
                
                // Create a gradient for testing
                const gradient = ctx.createLinearGradient(0, 0, 100, 100);
                gradient.addColorStop(0, 'white');
                gradient.addColorStop(1, 'black');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, 100, 100);
                
                const img = new Image();
                img.onload = async () => {
                    try {
                        const result = await depthEstimator.estimateDepth(img);
                        if (result && result.depthMap && result.width && result.height) {
                            log('✓ Depth estimation completed successfully');
                            log(`  - Depth map size: ${result.width}x${result.height}`);
                            log(`  - Depth values range: ${Math.min(...result.depthMap)} to ${Math.max(...result.depthMap)}`);
                        } else {
                            throw new Error('Invalid depth estimation result');
                        }
                    } catch (error) {
                        log('✗ Depth estimation failed: ' + error.message);
                    }
                };
                img.src = canvas.toDataURL();
                
            } catch (error) {
                log('✗ Depth estimation setup failed: ' + error.message);
            }
        }

        // Run tests automatically when page loads
        window.addEventListener('load', () => {
            setTimeout(runTests, 1000);
        });
    </script>
</body>
</html>

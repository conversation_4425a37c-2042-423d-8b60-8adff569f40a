@echo off
echo 🚀 Starting 2D to 3D Environment Generator Server...
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

echo ✓ Python found
echo 📁 Starting server in current directory...
echo.

REM Start the Python server
python start_server.py

pause

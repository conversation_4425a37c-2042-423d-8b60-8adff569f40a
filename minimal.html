<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
        }
        .upload-area {
            border: 3px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            background: #fafafa;
            margin: 20px 0;
        }
        .upload-area:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }
        .upload-area.dragover {
            border-color: #667eea;
            background: #e8f0ff;
        }
        .preview {
            margin: 20px 0;
            text-align: center;
        }
        .preview img {
            max-width: 400px;
            max-height: 300px;
            border-radius: 8px;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 20px 0;
        }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .generate-btn {
            background: #4CAF50;
            font-size: 16px;
            padding: 15px 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Minimal 2D to 3D Converter</h1>
        
        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <h3>Click here to upload an image</h3>
            <p>Or drag and drop an image file</p>
            <input type="file" id="fileInput" accept="image/*" style="display: none;">
        </div>
        
        <div class="preview" id="preview" style="display: none;">
            <img id="previewImage" alt="Preview">
            <br>
            <button onclick="removeImage()">Remove Image</button>
            <button id="generateBtn" class="generate-btn" onclick="generateEnvironment()" disabled>Generate 3D Environment</button>
        </div>
        
        <div class="log" id="log"></div>
        
        <canvas id="canvas3d" width="600" height="400" style="border: 1px solid #ddd; display: none;"></canvas>
        
        <div id="exportSection" style="display: none;">
            <button onclick="exportGLB()">Export GLB for Blender</button>
        </div>
    </div>

    <!-- Three.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://threejs.org/examples/js/controls/OrbitControls.js"></script>
    <script src="https://threejs.org/examples/js/exporters/GLTFExporter.js"></script>

    <script>
        let currentImage = null;
        let scene, camera, renderer, controls, mesh;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        log('Page loaded - setting up upload...');
        
        // File input change handler
        document.getElementById('fileInput').addEventListener('change', function(e) {
            log('File input changed');
            const file = e.target.files[0];
            if (file) {
                log(`File selected: ${file.name} (${file.type}, ${(file.size/1024).toFixed(1)}KB)`);
                loadImage(file);
            } else {
                log('No file selected');
            }
        });
        
        // Drag and drop
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            e.stopPropagation();
            this.classList.add('dragover');
            log('Drag over');
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            e.stopPropagation();
            this.classList.remove('dragover');
            log('Drag leave');
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            e.stopPropagation();
            this.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            log(`Files dropped: ${files.length}`);
            
            if (files.length > 0) {
                const file = files[0];
                log(`Processing: ${file.name}`);
                loadImage(file);
            }
        });
        
        function loadImage(file) {
            if (!file.type.startsWith('image/')) {
                log('ERROR: Not an image file');
                alert('Please select an image file (JPG, PNG, WebP)');
                return;
            }
            
            log('Reading file...');
            const reader = new FileReader();
            
            reader.onload = function(e) {
                log('File read complete');
                const img = new Image();
                
                img.onload = function() {
                    log(`Image loaded: ${img.width}x${img.height}`);
                    currentImage = img;
                    
                    // Show preview
                    document.getElementById('previewImage').src = e.target.result;
                    document.getElementById('preview').style.display = 'block';
                    document.querySelector('.upload-area').style.display = 'none';
                    document.getElementById('generateBtn').disabled = false;
                    
                    log('✓ Image ready for 3D conversion');
                };
                
                img.onerror = function() {
                    log('ERROR: Failed to load image');
                    alert('Failed to load image');
                };
                
                img.src = e.target.result;
            };
            
            reader.onerror = function() {
                log('ERROR: Failed to read file');
                alert('Failed to read file');
            };
            
            reader.readAsDataURL(file);
        }
        
        function removeImage() {
            log('Removing image...');
            currentImage = null;
            document.getElementById('preview').style.display = 'none';
            document.querySelector('.upload-area').style.display = 'block';
            document.getElementById('generateBtn').disabled = true;
            document.getElementById('canvas3d').style.display = 'none';
            document.getElementById('exportSection').style.display = 'none';
            document.getElementById('fileInput').value = '';
        }
        
        function generateEnvironment() {
            if (!currentImage) {
                log('ERROR: No image loaded');
                return;
            }
            
            log('🚀 Starting 3D generation...');
            
            try {
                // Initialize Three.js if not done
                if (!scene) {
                    initThreeJS();
                }
                
                // Generate depth map
                log('Creating depth map...');
                const depthData = createDepthMap(currentImage);
                
                // Create 3D mesh
                log('Creating 3D mesh...');
                create3DMesh(depthData);
                
                // Show canvas
                document.getElementById('canvas3d').style.display = 'block';
                document.getElementById('exportSection').style.display = 'block';
                
                log('✓ 3D environment generated successfully!');
                
            } catch (error) {
                log('ERROR: ' + error.message);
                console.error(error);
                alert('Error generating 3D environment: ' + error.message);
            }
        }
        
        function initThreeJS() {
            log('Initializing Three.js...');
            
            const canvas = document.getElementById('canvas3d');
            
            // Scene
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x87CEEB); // Sky blue
            
            // Camera
            camera = new THREE.PerspectiveCamera(75, canvas.width / canvas.height, 0.1, 1000);
            camera.position.set(0, 5, 10);
            
            // Renderer
            renderer = new THREE.WebGLRenderer({ canvas: canvas });
            renderer.setSize(canvas.width, canvas.height);
            
            // Controls
            controls = new THREE.OrbitControls(camera, renderer.domElement);
            
            // Lighting
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(10, 10, 5);
            scene.add(directionalLight);
            
            // Start render loop
            animate();
            
            log('✓ Three.js initialized');
        }
        
        function animate() {
            requestAnimationFrame(animate);
            if (controls) controls.update();
            if (renderer && scene && camera) renderer.render(scene, camera);
        }
        
        function createDepthMap(img) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = img.naturalWidth;
            canvas.height = img.naturalHeight;
            ctx.drawImage(img, 0, 0);
            
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;
            const depthMap = new Float32Array(canvas.width * canvas.height);
            
            // Simple luminance-based depth
            for (let i = 0; i < data.length; i += 4) {
                const r = data[i];
                const g = data[i + 1];
                const b = data[i + 2];
                const luminance = (r + g + b) / 3;
                depthMap[i / 4] = luminance / 255;
            }
            
            return {
                depthMap: depthMap,
                width: canvas.width,
                height: canvas.height,
                texture: new THREE.CanvasTexture(canvas)
            };
        }
        
        function create3DMesh(depthData) {
            // Remove existing mesh
            if (mesh) {
                scene.remove(mesh);
            }
            
            // Create plane geometry
            const geometry = new THREE.PlaneGeometry(20, 20, 63, 63);
            const vertices = geometry.attributes.position.array;
            
            // Apply depth displacement
            for (let i = 0; i < vertices.length; i += 3) {
                const x = vertices[i];
                const z = vertices[i + 2];
                
                // Map to image coordinates
                const imgX = Math.floor(((x + 10) / 20) * depthData.width);
                const imgY = Math.floor(((z + 10) / 20) * depthData.height);
                const index = Math.min(imgY * depthData.width + imgX, depthData.depthMap.length - 1);
                
                // Apply depth
                vertices[i + 1] = depthData.depthMap[index] * 3; // Height multiplier
            }
            
            geometry.attributes.position.needsUpdate = true;
            geometry.computeVertexNormals();
            
            // Create material with texture
            const material = new THREE.MeshLambertMaterial({
                map: depthData.texture,
                side: THREE.DoubleSide
            });
            
            // Create mesh
            mesh = new THREE.Mesh(geometry, material);
            scene.add(mesh);
        }
        
        function exportGLB() {
            if (!mesh) {
                log('ERROR: No 3D mesh to export');
                return;
            }
            
            log('📦 Exporting GLB...');
            
            const exporter = new THREE.GLTFExporter();
            
            exporter.parse(scene, function(result) {
                const blob = new Blob([result], { type: 'application/octet-stream' });
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                
                const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
                link.href = url;
                link.download = `3d_environment_${timestamp}.glb`;
                link.click();
                
                URL.revokeObjectURL(url);
                log('✓ GLB exported: ' + link.download);
                
            }, { binary: true, embedImages: true });
        }
        
        log('✓ All functions loaded and ready');
    </script>
</body>
</html>

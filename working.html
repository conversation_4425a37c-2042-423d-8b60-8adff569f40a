<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>2D to 3D Environment Generator - Working Version</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>2D Image to 3D Environment Generator</h1>
            <p>Upload a 2D image and convert it to a 3D environment exportable to Blender</p>
        </header>

        <main>
            <div class="upload-section">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-content">
                        <svg class="upload-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                            <polyline points="7,10 12,15 17,10"></polyline>
                            <line x1="12" y1="15" x2="12" y2="3"></line>
                        </svg>
                        <h3>Drop your image here or click to browse</h3>
                        <p>Supports JPG, PNG, WebP formats</p>
                    </div>
                    <input type="file" id="imageInput" accept="image/*" hidden>
                </div>
                
                <div class="image-preview" id="imagePreview" style="display: none;">
                    <img id="previewImg" alt="Preview">
                    <button id="removeImage" class="remove-btn">×</button>
                </div>
            </div>

            <div class="controls-section">
                <div class="control-group">
                    <label for="depthStrength">Depth Strength:</label>
                    <input type="range" id="depthStrength" min="0.1" max="2.0" step="0.1" value="1.0">
                    <span id="depthValue">1.0</span>
                </div>
                
                <div class="control-group">
                    <label for="meshResolution">Mesh Resolution:</label>
                    <select id="meshResolution">
                        <option value="32">Low (32x32)</option>
                        <option value="64" selected>Medium (64x64)</option>
                        <option value="128">High (128x128)</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label for="environmentType">Environment Type:</label>
                    <select id="environmentType">
                        <option value="landscape">Landscape</option>
                        <option value="room">Indoor Room</option>
                        <option value="terrain">Terrain</option>
                    </select>
                </div>
                
                <button id="generateBtn" class="generate-btn" disabled>Generate 3D Environment</button>
            </div>

            <div class="viewer-section">
                <div class="viewer-container">
                    <canvas id="threejsCanvas"></canvas>
                    <div class="viewer-controls">
                        <button id="resetView">Reset View</button>
                        <button id="exportGLB" disabled>Export GLB</button>
                    </div>
                </div>
                
                <div class="progress-section" id="progressSection" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <p id="progressText">Processing...</p>
                </div>
            </div>

            <div id="debugLog" style="background: #f0f0f0; padding: 10px; margin: 20px 0; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;"></div>
        </main>

        <footer>
            <p>Built with Three.js - Working Version</p>
        </footer>
    </div>

    <!-- Load Three.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://threejs.org/examples/js/controls/OrbitControls.js"></script>
    <script src="https://threejs.org/examples/js/exporters/GLTFExporter.js"></script>

    <script>
        function log(message) {
            const debugLog = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            debugLog.innerHTML += `[${timestamp}] ${message}<br>`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }
        
        let currentImage = null;
        let scene, camera, renderer, controls, generatedMesh;
        
        log('Initializing 2D to 3D converter...');
        
        document.addEventListener('DOMContentLoaded', function() {
            log('DOM loaded, setting up application...');
            
            // Initialize Three.js
            initThreeJS();
            
            // Setup upload functionality
            setupUpload();
            
            // Setup controls
            setupControls();
            
            log('✓ Application ready');
        });
        
        function initThreeJS() {
            const canvas = document.getElementById('threejsCanvas');
            
            // Scene
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x222222);
            
            // Camera
            camera = new THREE.PerspectiveCamera(75, canvas.clientWidth / canvas.clientHeight, 0.1, 1000);
            camera.position.set(0, 5, 10);
            
            // Renderer
            renderer = new THREE.WebGLRenderer({ canvas: canvas, antialias: true });
            renderer.setSize(canvas.clientWidth, canvas.clientHeight);
            renderer.shadowMap.enabled = true;
            
            // Controls
            controls = new THREE.OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;
            
            // Lighting
            const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(10, 10, 5);
            scene.add(directionalLight);
            
            // Start render loop
            animate();
            
            log('✓ Three.js initialized');
        }
        
        function animate() {
            requestAnimationFrame(animate);
            controls.update();
            renderer.render(scene, camera);
        }
        
        function setupUpload() {
            const uploadArea = document.getElementById('uploadArea');
            const imageInput = document.getElementById('imageInput');
            const imagePreview = document.getElementById('imagePreview');
            const previewImg = document.getElementById('previewImg');
            const removeImageBtn = document.getElementById('removeImage');
            
            uploadArea.addEventListener('click', () => imageInput.click());
            
            imageInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) loadImage(file);
            });
            
            removeImageBtn.addEventListener('click', function() {
                uploadArea.style.display = 'block';
                imagePreview.style.display = 'none';
                imageInput.value = '';
                currentImage = null;
                document.getElementById('generateBtn').disabled = true;
            });
            
            // Drag and drop
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', (e) => {
                e.preventDefault();
                if (!uploadArea.contains(e.relatedTarget)) {
                    uploadArea.classList.remove('dragover');
                }
            });
            
            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) loadImage(files[0]);
            });
        }
        
        function setupControls() {
            const generateBtn = document.getElementById('generateBtn');
            const exportGLBBtn = document.getElementById('exportGLB');
            const resetViewBtn = document.getElementById('resetView');
            const depthStrength = document.getElementById('depthStrength');
            const depthValue = document.getElementById('depthValue');
            
            generateBtn.addEventListener('click', generate3D);
            exportGLBBtn.addEventListener('click', exportGLB);
            resetViewBtn.addEventListener('click', () => {
                camera.position.set(0, 5, 10);
                controls.reset();
            });
            
            depthStrength.addEventListener('input', (e) => {
                depthValue.textContent = e.target.value;
            });
        }
        
        function loadImage(file) {
            if (!file.type.startsWith('image/')) {
                alert('Please select a valid image file.');
                return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    currentImage = img;
                    
                    const previewImg = document.getElementById('previewImg');
                    const uploadArea = document.getElementById('uploadArea');
                    const imagePreview = document.getElementById('imagePreview');
                    
                    previewImg.src = e.target.result;
                    uploadArea.style.display = 'none';
                    imagePreview.style.display = 'block';
                    document.getElementById('generateBtn').disabled = false;
                    
                    log(`✓ Image loaded: ${img.width}x${img.height}`);
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
        
        async function generate3D() {
            if (!currentImage) return;
            
            log('🚀 Starting 3D generation...');
            showProgress(true);
            updateProgress(10, 'Processing image...');
            
            try {
                // Get settings
                const depthStrength = parseFloat(document.getElementById('depthStrength').value);
                const resolution = parseInt(document.getElementById('meshResolution').value);
                
                // Simple depth estimation
                updateProgress(30, 'Estimating depth...');
                const depthData = await estimateDepth(currentImage);
                
                // Generate mesh
                updateProgress(60, 'Creating 3D mesh...');
                await generateMesh(depthData, resolution, depthStrength);
                
                updateProgress(100, 'Complete!');
                log('✓ 3D environment generated');
                
                document.getElementById('exportGLB').disabled = false;
                setTimeout(() => showProgress(false), 1000);
                
            } catch (error) {
                log('✗ Error: ' + error.message);
                alert('Error generating 3D environment: ' + error.message);
                showProgress(false);
            }
        }
        
        function estimateDepth(img) {
            return new Promise((resolve) => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                canvas.width = img.naturalWidth;
                canvas.height = img.naturalHeight;
                ctx.drawImage(img, 0, 0);
                
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                const data = imageData.data;
                const depthMap = new Float32Array(canvas.width * canvas.height);
                
                for (let i = 0; i < data.length; i += 4) {
                    const r = data[i];
                    const g = data[i + 1];
                    const b = data[i + 2];
                    const luminance = 0.299 * r + 0.587 * g + 0.114 * b;
                    depthMap[Math.floor(i / 4)] = luminance / 255.0;
                }
                
                resolve({
                    depthMap: depthMap,
                    width: canvas.width,
                    height: canvas.height,
                    imageData: imageData
                });
            });
        }
        
        async function generateMesh(depthData, resolution, depthStrength) {
            // Clear previous mesh
            if (generatedMesh) {
                scene.remove(generatedMesh);
            }
            
            // Create texture
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = depthData.width;
            canvas.height = depthData.height;
            ctx.putImageData(depthData.imageData, 0, 0);
            
            const texture = new THREE.CanvasTexture(canvas);
            texture.flipY = false;
            
            // Create geometry
            const geometry = new THREE.PlaneGeometry(20, 20, resolution - 1, resolution - 1);
            const vertices = geometry.attributes.position.array;
            
            // Apply depth
            for (let i = 0; i < vertices.length; i += 3) {
                const x = vertices[i];
                const z = vertices[i + 2];
                
                const imgX = Math.floor(((x + 10) / 20) * depthData.width);
                const imgY = Math.floor(((z + 10) / 20) * depthData.height);
                const depthIndex = Math.min(imgY * depthData.width + imgX, depthData.depthMap.length - 1);
                
                vertices[i + 1] = depthData.depthMap[depthIndex] * depthStrength * 3;
            }
            
            geometry.attributes.position.needsUpdate = true;
            geometry.computeVertexNormals();
            
            // Create material and mesh
            const material = new THREE.MeshLambertMaterial({ map: texture, side: THREE.DoubleSide });
            generatedMesh = new THREE.Mesh(geometry, material);
            
            scene.add(generatedMesh);
        }
        
        async function exportGLB() {
            if (!generatedMesh) return;
            
            log('📦 Exporting GLB...');
            showProgress(true);
            updateProgress(50, 'Preparing export...');
            
            try {
                const exporter = new THREE.GLTFExporter();
                const options = { binary: true, embedImages: true };
                
                exporter.parse(scene, function(result) {
                    const blob = new Blob([result], { type: 'application/octet-stream' });
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    
                    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
                    link.href = url;
                    link.download = `3d_environment_${timestamp}.glb`;
                    link.click();
                    
                    URL.revokeObjectURL(url);
                    
                    updateProgress(100, 'Export complete!');
                    log('✓ GLB exported successfully');
                    
                    setTimeout(() => showProgress(false), 1000);
                }, options);
                
            } catch (error) {
                log('✗ Export error: ' + error.message);
                alert('Export failed: ' + error.message);
                showProgress(false);
            }
        }
        
        function showProgress(show) {
            document.getElementById('progressSection').style.display = show ? 'block' : 'none';
        }
        
        function updateProgress(percentage, text) {
            document.getElementById('progressFill').style.width = percentage + '%';
            document.getElementById('progressText').textContent = text;
        }
    </script>
</body>
</html>

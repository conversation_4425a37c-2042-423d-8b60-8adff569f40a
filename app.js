class ImageTo3DApp {
    constructor() {
        this.depthEstimator = new DepthEstimator();
        this.meshGenerator = new MeshGenerator();
        this.glbExporter = new GLBExporter();
        
        this.currentImage = null;
        this.currentDepthData = null;
        this.isProcessing = false;
        
        this.initializeEventListeners();
        this.initializeThreeJS();
    }

    initializeEventListeners() {
        // File upload handling
        const uploadArea = document.getElementById('uploadArea');
        const imageInput = document.getElementById('imageInput');
        const removeImageBtn = document.getElementById('removeImage');
        
        uploadArea.addEventListener('click', () => {
            console.log('Upload area clicked');
            imageInput.click();
        });
        uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
        uploadArea.addEventListener('dragenter', this.handleDragEnter.bind(this));
        uploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
        uploadArea.addEventListener('drop', this.handleDrop.bind(this));
        imageInput.addEventListener('change', this.handleFileSelect.bind(this));
        removeImageBtn.addEventListener('click', this.removeImage.bind(this));

        // Prevent default drag behaviors on document
        document.addEventListener('dragover', (e) => e.preventDefault());
        document.addEventListener('drop', (e) => e.preventDefault());

        // Control handlers
        const depthStrength = document.getElementById('depthStrength');
        const depthValue = document.getElementById('depthValue');
        depthStrength.addEventListener('input', (e) => {
            depthValue.textContent = e.target.value;
        });

        // Button handlers
        const generateBtn = document.getElementById('generateBtn');
        const resetViewBtn = document.getElementById('resetView');
        const exportGLBBtn = document.getElementById('exportGLB');
        
        generateBtn.addEventListener('click', this.generateEnvironment.bind(this));
        resetViewBtn.addEventListener('click', () => this.meshGenerator.resetView());
        exportGLBBtn.addEventListener('click', this.exportGLB.bind(this));

        // Window resize handler
        window.addEventListener('resize', this.handleResize.bind(this));
    }

    initializeThreeJS() {
        const canvas = document.getElementById('threejsCanvas');
        this.meshGenerator.initializeThreeJS(canvas);
    }

    handleDragEnter(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.add('dragover');
    }

    handleDragOver(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    handleDragLeave(e) {
        e.preventDefault();
        e.stopPropagation();
        // Only remove dragover if we're leaving the upload area itself
        if (!e.currentTarget.contains(e.relatedTarget)) {
            e.currentTarget.classList.remove('dragover');
        }
    }

    handleDrop(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            console.log('File dropped:', files[0].name);
            this.loadImage(files[0]);
        }
    }

    handleFileSelect(e) {
        console.log('File input changed');
        const file = e.target.files[0];
        if (file) {
            console.log('File selected via input:', file.name);
            this.loadImage(file);
        } else {
            console.log('No file selected');
        }
    }

    loadImage(file) {
        console.log('Loading image:', file.name, 'Type:', file.type, 'Size:', file.size);

        if (!file.type.startsWith('image/')) {
            alert('Please select a valid image file (JPG, PNG, WebP).');
            return;
        }

        // Check file size (limit to 10MB)
        if (file.size > 10 * 1024 * 1024) {
            alert('Image file is too large. Please select an image smaller than 10MB.');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            console.log('File read successfully');
            const img = new Image();
            img.onload = () => {
                console.log('Image loaded:', img.width + 'x' + img.height);
                this.currentImage = img;
                this.displayImagePreview(e.target.result);
                this.enableGeneration();
            };
            img.onerror = () => {
                console.error('Failed to load image');
                alert('Failed to load the image. Please try a different file.');
            };
            img.src = e.target.result;
        };
        reader.onerror = () => {
            console.error('Failed to read file');
            alert('Failed to read the file. Please try again.');
        };
        reader.readAsDataURL(file);
    }

    displayImagePreview(src) {
        const uploadArea = document.getElementById('uploadArea');
        const imagePreview = document.getElementById('imagePreview');
        const previewImg = document.getElementById('previewImg');
        
        uploadArea.style.display = 'none';
        imagePreview.style.display = 'block';
        previewImg.src = src;
    }

    removeImage() {
        const uploadArea = document.getElementById('uploadArea');
        const imagePreview = document.getElementById('imagePreview');
        const imageInput = document.getElementById('imageInput');
        
        uploadArea.style.display = 'block';
        imagePreview.style.display = 'none';
        imageInput.value = '';
        
        this.currentImage = null;
        this.currentDepthData = null;
        this.disableGeneration();
    }

    enableGeneration() {
        const generateBtn = document.getElementById('generateBtn');
        generateBtn.disabled = false;
    }

    disableGeneration() {
        const generateBtn = document.getElementById('generateBtn');
        const exportGLBBtn = document.getElementById('exportGLB');
        generateBtn.disabled = true;
        exportGLBBtn.disabled = true;
    }

    async generateEnvironment() {
        if (!this.currentImage || this.isProcessing) return;

        this.isProcessing = true;
        this.showProgress(true);
        
        try {
            // Get control values
            const depthStrength = parseFloat(document.getElementById('depthStrength').value);
            const meshResolution = parseInt(document.getElementById('meshResolution').value);
            const environmentType = document.getElementById('environmentType').value;

            // Update progress
            this.updateProgress(10, 'Analyzing image...');

            // Estimate depth
            this.currentDepthData = await this.depthEstimator.enhancedDepthEstimation(
                this.currentImage,
                environmentType
            );

            this.updateProgress(50, 'Generating 3D mesh...');

            // Generate mesh
            await this.meshGenerator.generateMesh(
                this.currentDepthData,
                this.currentImage,
                meshResolution,
                depthStrength,
                environmentType,
                (progress) => this.updateProgress(50 + progress * 0.4, 'Creating 3D environment...')
            );

            this.updateProgress(100, 'Complete!');
            
            // Enable export
            document.getElementById('exportGLB').disabled = false;
            
            setTimeout(() => this.showProgress(false), 1000);

        } catch (error) {
            console.error('Error generating environment:', error);
            alert('Error generating 3D environment. Please try again.');
            this.showProgress(false);
        } finally {
            this.isProcessing = false;
        }
    }

    async exportGLB() {
        if (!this.meshGenerator.generatedMesh) {
            alert('Please generate a 3D environment first.');
            return;
        }

        try {
            this.showProgress(true);
            this.updateProgress(50, 'Preparing export...');

            const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
            const filename = `3d_environment_${timestamp}`;

            await this.glbExporter.exportMesh(this.meshGenerator.generatedMesh, filename);

            this.updateProgress(100, 'Export complete!');
            
            // Show export statistics
            const stats = this.glbExporter.getExportStats(this.meshGenerator.scene);
            const message = `Export successful!\n\nStatistics:\n- Vertices: ${stats.vertices}\n- Faces: ${stats.faces}\n- Materials: ${stats.materials}\n- Textures: ${stats.textures}`;
            alert(message);

            setTimeout(() => this.showProgress(false), 1000);

        } catch (error) {
            console.error('Error exporting GLB:', error);
            alert('Error exporting GLB file. Please try again.');
            this.showProgress(false);
        }
    }

    showProgress(show) {
        const progressSection = document.getElementById('progressSection');
        progressSection.style.display = show ? 'block' : 'none';
        
        if (!show) {
            this.updateProgress(0, '');
        }
    }

    updateProgress(percentage, text) {
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        
        progressFill.style.width = `${percentage}%`;
        progressText.textContent = text;
    }

    handleResize() {
        const canvas = document.getElementById('threejsCanvas');
        this.meshGenerator.resize(canvas.clientWidth, canvas.clientHeight);
    }
}

// Initialize the application when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new ImageTo3DApp();
});
